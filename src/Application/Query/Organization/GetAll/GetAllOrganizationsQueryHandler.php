<?php

declare(strict_types=1);

namespace App\Application\Query\Organization\GetAll;

use App\Application\Query\ItemsWithMetadataResponse;
use App\Application\Query\Organization\Get\OrganizationResponse;
use App\Application\Service\SortServiceInterface;
use App\Domain\Model\Organization\Organization;
use App\Domain\Model\Organization\OrganizationRepositoryInterface;
use App\Domain\Model\Organization\OrganizationValidatorInterface;

final readonly class GetAllOrganizationsQueryHandler
{
    private const DEFAULT_SORT = [['attribute' => 'createdAt', 'direction' => 'desc']];

    public function __construct(
        private readonly OrganizationRepositoryInterface $repository,
        private readonly OrganizationValidatorInterface $validator,
        private readonly SortServiceInterface $sortService
    ) {}

    public function __invoke(GetAllOrganizationsQuery $query): ?ItemsWithMetadataResponse
    {
        $filters = $query->getFilters();

        $filterErrors = $this->validator->validateFilters($filters);
        if ($filterErrors->hasErrors()) {
            return ItemsWithMetadataResponse::createEmpty();
        }

        $sort = $this->sortService->formatSort($this->getAllowedSortAttributes(), $query->getSort()) ?: self::DEFAULT_SORT;

        $resultWithMetadata = $this->repository->search($query->getLimit(), $query->getOffset(), $filters, $sort);

        return ItemsWithMetadataResponse::create(
            $resultWithMetadata,
            fn (Organization $organization) => OrganizationResponse::fromOrganization($organization)
        );
    }

    /**
     * @return string[]
     */
    private function getAllowedSortAttributes(): array
    {
        return ['createdAt', 'name'];
    }
}
