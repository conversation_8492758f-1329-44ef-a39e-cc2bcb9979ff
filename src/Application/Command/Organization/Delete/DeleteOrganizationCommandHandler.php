<?php

declare(strict_types=1);

namespace App\Application\Command\Organization\Delete;

use App\Domain\Model\Organization\OrganizationRepositoryInterface;

final readonly class DeleteOrganizationCommandHandler
{
    public function __construct(
        private readonly OrganizationRepositoryInterface $organizationRepository
    ) {}

    public function __invoke(DeleteOrganizationCommand $command): bool
    {
        $organization = $this->organizationRepository->find($command->getId());

        if (null === $organization) {
            return false; // Organization not found
        }

        $organization->markAsDeleted();
        $this->organizationRepository->save($organization);

        return true; // Successfully deleted
    }
}
