<?php

declare(strict_types=1);

namespace App\Infrastructure\Delivery\Api\V1\Organization;

use App\Application\Command\Organization\Create\CreateOrganizationCommand;
use App\Infrastructure\Http\RequestMapper;
use OpenApi\Attributes as OA;
use App\Domain\Model\Bus\Command\CommandBusInterface;
use App\Infrastructure\Http\ResponseMapper;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;

#[OA\Delete(
    path: '/tenants/v1/{_locale}/organizations/{id}',
    summary: 'Delete Organization (Soft Delete)',
    security: [['bearerAuth' => []]],
    tags: ['Organizations'],
    parameters: [
        new OA\Parameter(ref: '#/components/parameters/_locale'),
        new OA\Parameter(
            name: 'id',
            description: 'Organization ID',
            in: 'path',
            required: true,
            schema: new OA\Schema(type: 'string', format: 'uuid')
        ),
    ],
    responses: [
        new OA\Response(
            response: Response::HTTP_NO_CONTENT,
            description: 'Organization deleted successfully'
        ),
        new OA\Response(
            response: Response::HTTP_NOT_FOUND,
            description: 'Organization not found'
        ),
        new OA\Response(ref: '#/components/responses/InternalServerErrorResponse', response: Response::HTTP_INTERNAL_SERVER_ERROR),
    ]
)]
final class DeleteOrganizationAction extends AbstractController
{
    public function __construct(
        private readonly CommandBusInterface $commandBus,
        private readonly RequestMapper       $requestMapper,
        private readonly ResponseMapper      $responseMapper
    ) {}

    /**
     * @throws \ReflectionException
     */
    #[Route('/organizations/{id}', name: 'app_delete_organization', methods: ['DELETE'])]
    public function __invoke(Request $request): JsonResponse
    {
        /** @var CreateOrganizationCommand $command */
        $command = $this->requestMapper->fromRequest($request, CreateOrganizationCommand::class);

        return $this->responseMapper->serializeResponse($this->commandBus->dispatch($command), Response::HTTP_CREATED);
    }
}
