<?php

declare(strict_types=1);

namespace App\Infrastructure\Delivery\Api\V1\Documentation\Response\Organization;

use App\Infrastructure\Delivery\Api\V1\Documentation\Schema\MetadataResponseSchema;
use App\Infrastructure\Delivery\Api\V1\Documentation\Schema\Organization\OrganizationResponseSchema;
use OpenApi\Attributes as OA;

#[OA\Response(
    response: 'CompanyListResponse',
    description: 'Successful company list response',
    content: new OA\JsonContent(
        examples: [
            'default' => new OA\Examples(
                example: 'default',
                summary: 'Default',
                value: [
                    'data' => [
                        'items' => [OrganizationResponseSchema::EXAMPLE_DEFAULT],
                        'metadata' => MetadataResponseSchema::EXAMPLE_DEFAULT,
                    ],
                ]
            ),
        ],
        required: ['data'],
        properties: [
            new OA\Property(
                property: 'data',
                required: ['items', 'metadata'],
                properties: [
                    new OA\Property(
                        property: 'items',
                        type: 'array',
                        items: new OA\Items(ref: '#/components/schemas/OrganizationResponseSchema'),
                    ),
                    new OA\Property(property: 'metadata', ref: '#/components/schemas/MetadataResponseSchema'),
                ],
            ),
        ],
    ),
)]
final class OrganizationListResponse
{
}
